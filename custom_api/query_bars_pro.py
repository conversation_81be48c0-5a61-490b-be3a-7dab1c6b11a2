def query_bars_pro(symbol, type_, source, count=-1, fields=None, data_type=1, start_datatime='', end_datatime=''):
    """
    获取一段时间获取N根bar数据
    :param symbol: str 合约唯一代码
    :param type_: str 数据类型, 如1D_BAR_DEPTH
    :param source: str 行情来源
    :param count: int bar数量, 默认-1, 表示全部返回
    :param fields: list 指定返回对象字段, 默认None, 表示全部返回
    :param data_type: int 数据返回类型, 0 表示pandas结构, 1 表示numpy结构, 2 表示dict结构, 默认1
    :param start_datatime: str bar的开始时间, 支持以下格式'%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S', '%Y%m%d%H%M%S', 默认'', 表示全部返回
    :param end_datatime: str bar的结束时间, 支持以下格式'%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S', '%Y%m%d%H%M%S', 默认'', 表示全部返回

    :return: Bar数据, 数据类型根据入参的data_type值而变
    :return: time(int):时间戳
    :return: open(float):开盘价
    :return: close(float):收盘价
    :return: high(float):最高价
    :return: low(float):最低价
    bars = md.query_bars_pro("EURUSDSP", "1D_BAR_DEPTH",  "UBS_HO", count=-1, data_type=DATA_TYPE_PANDAS, start_datatime="2021-12-10 00:00", end_datatime="2021-12-20 00:00")
    """
    return


