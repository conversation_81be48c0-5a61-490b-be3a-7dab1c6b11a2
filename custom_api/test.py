import requests
import pandas as pd
from typing import Optional, Union
from datetime import datetime
import pytz


def timestamp_to_beijing_time(
        timestamp: Union[int, float, str],
        format_str: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """
    将时间戳转换为北京时间

    Args:
        timestamp: 时间戳，支持以下格式：
                  - int/float: Unix时间戳（秒）
                  - str: 字符串格式的时间戳
        format_str: 输出时间格式，默认为 "%Y-%m-%d %H:%M:%S"

    Returns:
        格式化的北京时间字符串

    Examples:
        >>> timestamp_to_beijing_time(1640995200)
        '2022-01-01 08:00:00'
        >>> timestamp_to_beijing_time("1640995200")
        '2022-01-01 08:00:00'
        >>> timestamp_to_beijing_time(1640995200, "%Y年%m月%d日 %H时%M分%S秒")
        '2022年01月01日 08时00分00秒'
    """
    try:
        # 处理字符串类型的时间戳
        if isinstance(timestamp, str):
            timestamp = float(timestamp)

        # 创建UTC时间
        utc_time = datetime.fromtimestamp(timestamp, tz=pytz.UTC)

        # 转换为北京时间 (UTC+8)
        beijing_tz = pytz.timezone('Asia/Shanghai')
        beijing_time = utc_time.astimezone(beijing_tz)

        # 格式化输出
        return beijing_time.strftime(format_str)

    except (ValueError, TypeError, OSError) as e:
        raise ValueError(f"无效的时间戳格式: {timestamp}, 错误: {e}")


def current_beijing_time(format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    获取当前北京时间

    Args:
        format_str: 输出时间格式，默认为 "%Y-%m-%d %H:%M:%S"

    Returns:
        格式化的当前北京时间字符串
    """
    beijing_tz = pytz.timezone('Asia/Shanghai')
    beijing_time = datetime.now(beijing_tz)
    return beijing_time.strftime(format_str)


def beijing_time_to_timestamp(
        time_str: str,
        format_str: str = "%Y-%m-%d %H:%M:%S"
) -> int:
    """
    将北京时间字符串转换为时间戳

    Args:
        time_str: 北京时间字符串
        format_str: 输入时间格式，默认为 "%Y-%m-%d %H:%M:%S"

    Returns:
        Unix时间戳（秒）

    Examples:
        >>> beijing_time_to_timestamp("2022-01-01 08:00:00")
        1640995200
    """
    try:
        # 解析时间字符串
        dt = datetime.strptime(time_str, format_str)

        # 设置为北京时区
        beijing_tz = pytz.timezone('Asia/Shanghai')
        beijing_time = beijing_tz.localize(dt)

        # 转换为时间戳
        return int(beijing_time.timestamp())

    except ValueError as e:
        raise ValueError(f"时间格式错误: {time_str}, 期望格式: {format_str}, 错误: {e}")


def get_fx_daily_data(
        from_symbol: str,
        to_symbol: str,
        api_key: str,
        outputsize: str = "full"
) -> Optional[pd.DataFrame]:
    """
    获取外汇日线数据

    Args:
        from_symbol: 源货币符号 (如 'EUR')
        to_symbol: 目标货币符号 (如 'USD')
        api_key: AlphaVantage API密钥
        outputsize: 数据大小 ('compact' 或 'full')

    Returns:
        包含外汇数据的DataFrame，如果请求失败则返回None
    """
    url = "https://www.alphavantage.co/query"
    params = {
        "function": "FX_DAILY",
        "from_symbol": from_symbol,
        "to_symbol": to_symbol,
        "apikey": api_key,
        "outputsize": outputsize,
        "datatype": "json"
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # 检查HTTP错误
        data = response.json()

        # 检查API响应是否包含错误信息
        if "Error Message" in data:
            print(f"API错误: {data['Error Message']}")
            return None

        if "Note" in data:
            print(f"API提示: {data['Note']}")
            return None

        if "Time Series FX (Daily)" not in data:
            print("响应中未找到外汇数据")
            return None

        # 解析数据
        df = pd.DataFrame.from_dict(data["Time Series FX (Daily)"], orient="index")
        df = df.rename(columns={
            "1. open": "open",
            "2. high": "high",
            "3. low": "low",
            "4. close": "close"
        })

        df.index = pd.to_datetime(df.index)
        df = df.astype(float)
        df = df.sort_index()  # 按日期排序

        return df

    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except Exception as e:
        print(f"数据处理错误: {e}")
        return None


# 使用示例
if __name__ == "__main__":
    # 时间戳转换示例
    print("=== 时间戳转换示例 ===")
    utc_time = datetime.fromtimestamp(1754853300000, tz=pytz.UTC)
    print(utc_time)
    # # 示例时间戳
    # test_timestamps = [
    #     1640995200,  # 2022-01-01 00:00:00 UTC
    #     1672531200,  # 2023-01-01 00:00:00 UTC
    #     "1704067200",  # 2024-01-01 00:00:00 UTC (字符串格式)
    # ]
    #
    # for ts in test_timestamps:
    #     beijing_time = timestamp_to_beijing_time(ts)
    #     print(f"时间戳 {ts} -> 北京时间: {beijing_time}")
    #
    # # 自定义格式示例
    # custom_format = timestamp_to_beijing_time(1640995200, "%Y年%m月%d日 %H时%M分%S秒")
    # print(f"自定义格式: {custom_format}")
    #
    # # 当前北京时间
    # current_time = current_beijing_time()
    # print(f"当前北京时间: {current_time}")
    #
    # # 北京时间转时间戳
    # time_str = "2022-01-01 08:00:00"
    # timestamp = beijing_time_to_timestamp(time_str)
    # print(f"北京时间 {time_str} -> 时间戳: {timestamp}")
    #
    # print("\n=== 外汇数据获取示例 ===")
    # API_KEY = "6Z4T2DQ34W6E2HXM"  # 你的 AlphaVantage API Key
    #
    # # 获取EUR/USD汇率数据
    # df = get_fx_daily_data("EUR", "USD", API_KEY)
    #
    # if df is not None:
    #     print("数据获取成功!")
    #     print(f"数据形状: {df.shape}")
    #     print("\n最新5天数据:")
    #     print(df.head())
    # else:
    #     print("数据获取失败")
