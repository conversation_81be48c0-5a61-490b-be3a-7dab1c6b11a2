
import requests
import pandas as pd

API_KEY = "6Z4T2DQ34W6E2HXM"  # 你的 AlphaVantage API Key
FROM = "EUR"
TO = "USD"

url = f"https://www.alphavantage.co/query"
params = {
    "function": "FX_DAILY",  # 日线数据
    "from_symbol": FROM,
    "to_symbol": TO,
    "apikey": API_KEY,
    "outputsize": "full",  # 获取全部历史
    "datatype": "json"
}

response = requests.get(url, params=params)
data = response.json()

# 解析数据
df = pd.DataFrame.from_dict(data["Time Series FX (Daily)"], orient="index")
df = df.rename(columns={
    "1. open": "open",
    "2. high": "high",
    "3. low": "low",
    "4. close": "close"
})

df.index = pd.to_datetime(df.index)
df = df.astype(float)

print(df.head())
